"""
Tests for the stt_engine_wrapper module.
"""

import unittest
import tempfile
import os
import numpy as np
from unittest.mock import patch, MagicMock

# Add parent directory to path so we can import src modules
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.stt_engine_wrapper import STTEngineWrapper

class TestSTTEngineWrapper(unittest.TestCase):
    """Tests for the STTEngineWrapper class."""
    
    def setUp(self):
        """Set up the test environment."""
        # Mock the WhisperModel to avoid actual model loading
        self.patcher = patch('src.stt_engine_wrapper.WhisperModel')
        self.mock_whisper_model = self.patcher.start()
        
        # Mock instance returned by WhisperModel constructor
        self.mock_model_instance = MagicMock()
        self.mock_whisper_model.return_value = self.mock_model_instance
        
        # Mock transcribe method of model instance
        self.mock_model_instance.transcribe.return_value = (
            [MagicMock(text="This is a test transcription.")],  # segments
            MagicMock(language="en", language_probability=0.99)  # info
        )
        
        # Create a temporary directory for models
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Mock torch.cuda.is_available to always return False
        self.cuda_patcher = patch('src.stt_engine_wrapper.torch.cuda.is_available', return_value=False)
        self.cuda_patcher.start()
    
    def tearDown(self):
        """Clean up the test environment."""
        self.patcher.stop()
        self.cuda_patcher.stop()
        self.temp_dir.cleanup()
    
    def test_init(self):
        """Test initialization of STTEngineWrapper."""
        wrapper = STTEngineWrapper(model_dir=self.temp_dir.name)
        
        # Check that WhisperModel was instantiated (via _try_load_default_model)
        self.mock_whisper_model.assert_called_once()
        
        # Check default values
        self.assertEqual(wrapper.supported_languages, ["en", "it", "ro"])
        self.assertEqual(wrapper.device, "cpu")  # Because we mocked cuda.is_available
    
    def test_load_model(self):
        """Test loading a model."""
        wrapper = STTEngineWrapper(model_dir=self.temp_dir.name)
        
        # Reset mock to clear initialization call
        self.mock_whisper_model.reset_mock()
        
        # Call load_model for a specific language
        result = wrapper.load_model("it")
        
        # Check that WhisperModel was instantiated with correct arguments
        self.mock_whisper_model.assert_called_once_with(
            "large-v3",  # Default model name
            device="cpu",
            compute_type="float16",
            download_root=self.temp_dir.name
        )
        
        # Check that the model was added to the models dict
        self.assertIn("it", wrapper.models)
        self.assertEqual(wrapper.active_model_lang, "it")
        self.assertTrue(result)
    
    def test_transcribe(self):
        """Test transcribing audio."""
        wrapper = STTEngineWrapper(model_dir=self.temp_dir.name)
        
        # Create a simple audio chunk
        audio_chunk = np.zeros((1600,), dtype=np.int16)  # 100ms of silence at 16kHz
        
        # Test transcription
        with patch('src.stt_engine_wrapper.os.unlink') as mock_unlink:
            with patch('src.stt_engine_wrapper.wave.open') as mock_wave_open:
                # Mock wave file writer
                mock_wave_file = MagicMock()
                mock_wave_open.return_value.__enter__.return_value = mock_wave_file
                
                text = wrapper.transcribe(audio_chunk, lang_code="en")
                
                # Check that the model's transcribe method was called
                self.mock_model_instance.transcribe.assert_called_once()
                
                # Check that the correct text was returned
                self.assertEqual(text, "This is a test transcription.")
                
                # Check that temp file was deleted
                mock_unlink.assert_called_once()
    
    def test_audio_chunk_accumulation(self):
        """Test accumulation of audio chunks."""
        wrapper = STTEngineWrapper(model_dir=self.temp_dir.name)
        
        # Create sample audio chunks
        chunks = [np.zeros((1600,), dtype=np.int16) for _ in range(5)]
        
        # Start accumulating
        wrapper.start_accumulating()
        self.assertTrue(wrapper.is_accumulating)
        
        # Add chunks
        for chunk in chunks:
            wrapper.add_audio_chunk(chunk)
        
        # Check accumulated data
        self.assertEqual(len(wrapper.accumulated_audio), 5)
        
        # Stop accumulating and get data
        accumulated = wrapper.stop_accumulating()
        self.assertFalse(wrapper.is_accumulating)
        self.assertEqual(len(accumulated), 5)
        self.assertEqual(len(wrapper.accumulated_audio), 0)
    
    def test_set_active_language(self):
        """Test setting the active language."""
        wrapper = STTEngineWrapper(model_dir=self.temp_dir.name)
        
        # Reset mock to clear initialization call
        self.mock_whisper_model.reset_mock()
        
        # Set language to Romanian
        result = wrapper.set_active_language("ro")
        
        # Check that model was loaded
        self.assertTrue(result)
        self.assertEqual(wrapper.active_model_lang, "ro")
        self.assertIn("ro", wrapper.models)
        
        # Try setting an invalid language
        result = wrapper.set_active_language("invalid")
        self.assertFalse(result)
        self.assertEqual(wrapper.active_model_lang, "ro")  # Should not change

if __name__ == '__main__':
    unittest.main() 