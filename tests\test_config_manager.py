"""
Tests for the config_manager module.
"""

import unittest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add parent directory to path so we can import src.config_manager
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.config_manager import ConfigManager

class TestConfigManager(unittest.TestCase):
    """Tests for the ConfigManager class."""
    
    def setUp(self):
        """Set up the test environment."""
        # Create a temporary directory for the config file
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Patch os.getenv to return our temporary directory
        self.patcher = patch('os.getenv')
        self.mock_getenv = self.patcher.start()
        self.mock_getenv.return_value = self.temp_dir.name
    
    def tearDown(self):
        """Clean up the test environment."""
        self.patcher.stop()
        self.temp_dir.cleanup()
    
    def test_init_creates_directory(self):
        """Test that initialization creates the app data directory if it doesn't exist."""
        config_manager = ConfigManager()
        
        # Check that the directory was created
        self.assertTrue(os.path.exists(config_manager.app_data_dir))
    
    def test_default_config(self):
        """Test that the default configuration is used when no config file exists."""
        config_manager = ConfigManager()
        
        # Check default values
        self.assertEqual(config_manager.get_hotkey(), "<ctrl>+<alt>+r")
        self.assertEqual(config_manager.get_active_language(), "en")
    
    def test_save_and_load_config(self):
        """Test saving and loading the configuration."""
        config_manager = ConfigManager()
        
        # Modify configuration
        config_manager.set_hotkey("<ctrl>+<alt>+t")
        config_manager.set_active_language("it")
        
        # Create a new instance to load the saved configuration
        config_manager2 = ConfigManager()
        
        # Check that the values were saved and loaded correctly
        self.assertEqual(config_manager2.get_hotkey(), "<ctrl>+<alt>+t")
        self.assertEqual(config_manager2.get_active_language(), "it")
    
    def test_invalid_language(self):
        """Test that setting an invalid language is handled correctly."""
        config_manager = ConfigManager()
        original_lang = config_manager.get_active_language()
        
        # Try to set an invalid language
        config_manager.set_active_language("invalid")
        
        # Check that the language didn't change
        self.assertEqual(config_manager.get_active_language(), original_lang)
    
    def test_get_config_with_default(self):
        """Test getting a configuration value with a default."""
        config_manager = ConfigManager()
        
        # Get a non-existent key with a default value
        value = config_manager.get_config("non_existent", "default_value")
        
        # Check that the default value was returned
        self.assertEqual(value, "default_value")

if __name__ == '__main__':
    unittest.main() 