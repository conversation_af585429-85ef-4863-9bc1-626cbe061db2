"""
STT Engine wrapper for TalktoWrite.

This module handles interaction with Speech-to-Text models using faster-whisper.
"""

import logging
import os
import numpy as np
import io
import wave
from pathlib import Path
import tempfile
import threading
from faster_whisper import WhisperModel
import torch

# Setup logging
logger = logging.getLogger(__name__)

class STTEngineWrapper:
    """Wrapper for Speech-to-Text engine using faster-whisper."""
    
    def __init__(self, model_dir=None, compute_type=None):
        """
        Initialize the STT engine wrapper.
        
        Args:
            model_dir: Directory where models are stored (or None for HF cache)
            compute_type: Compute type for inference ("float16", "float32", "int8_float16")
                          If None, will be automatically set based on device
        """
        if model_dir is None:
            # Default to 'models' directory in the application directory
            self.model_dir = Path(__file__).parent.parent / "models"
        else:
            self.model_dir = Path(model_dir)
        
        self.models = {}
        self.active_model_lang = None
        self.supported_languages = ["en", "it", "ro"]
        
        # Flag to track if we're accumulating audio for a full transcription
        self.is_accumulating = False
        self.accumulated_audio = []
        
        # Check for CUDA availability
        self.has_cuda = torch.cuda.is_available()
        if self.has_cuda:
            device_name = torch.cuda.get_device_name(0)
            logger.info(f"CUDA available: {device_name}")
            self.device = "cuda"
            # Set default compute type if not specified
            self.compute_type = compute_type or "float16"
        else:
            logger.info("CUDA not available, using CPU")
            self.device = "cpu"
            # Use float32 on CPU to avoid compatibility issues
            self.compute_type = compute_type or "float32"
            logger.info(f"Using compute_type={self.compute_type} on CPU")
        
        # Map of language codes to specific model paths or identifiers
        # Default is to use the large-v3 model for all languages
        self.language_models = {
            "en": "large-v3",
            "it": "large-v3",  # Can be replaced with "bofenghuang/whisper-large-v3-distil-it-v0.2"
            "ro": "large-v3",  # Can be replaced with "TransferRapid/whisper-large-v3-turbo_ro"
        }
        
        # Try to load the default model
        self._try_load_default_model()
        
        logger.info(f"STT Engine initialized with model directory: {self.model_dir}")
    
    def _try_load_default_model(self):
        """Try to load the default model for the first supported language."""
        try:
            default_lang = self.supported_languages[0]
            self.load_model(default_lang)
        except Exception as e:
            logger.warning(f"Could not load default model: {e}")
    
    def load_model(self, lang_code):
        """
        Load a model for the specified language.
        
        Args:
            lang_code: Language code (e.g., 'en', 'it', 'ro')
            
        Returns:
            True if the model was loaded successfully, False otherwise
        """
        if lang_code not in self.supported_languages:
            logger.warning(f"Unsupported language: {lang_code}")
            return False
        
        try:
            model_name = self.language_models.get(lang_code, "large-v3")
            logger.info(f"Loading model for language: {lang_code}, model: {model_name}")
            
            # Check if model is already loaded
            if lang_code in self.models:
                logger.info(f"Model for {lang_code} already loaded")
                self.active_model_lang = lang_code
                return True
            
            # Load the model
            model = WhisperModel(
                model_name,
                device=self.device,
                compute_type=self.compute_type,
                download_root=str(self.model_dir) if self.model_dir.exists() else None
            )
            
            self.models[lang_code] = model
            self.active_model_lang = lang_code
            logger.info(f"Model for {lang_code} loaded successfully")
            return True
        except Exception as e:
            logger.error(f"Error loading model for {lang_code}: {e}")
            return False
    
    def unload_model(self, lang_code):
        """
        Unload a model for the specified language.
        
        Args:
            lang_code: Language code (e.g., 'en', 'it', 'ro')
            
        Returns:
            True if the model was unloaded successfully, False otherwise
        """
        if lang_code in self.models:
            try:
                if self.active_model_lang == lang_code:
                    self.active_model_lang = None
                
                # Remove reference to allow garbage collection
                del self.models[lang_code]
                logger.info(f"Model for {lang_code} unloaded successfully")
                return True
            except Exception as e:
                logger.error(f"Error unloading model for {lang_code}: {e}")
                return False
        else:
            logger.warning(f"No model loaded for {lang_code}")
            return False
    
    def start_accumulating(self):
        """Start accumulating audio chunks for transcription."""
        self.is_accumulating = True
        self.accumulated_audio = []
        logger.debug("Started accumulating audio")
    
    def stop_accumulating(self):
        """Stop accumulating audio chunks and return accumulated data."""
        self.is_accumulating = False
        result = self.accumulated_audio
        self.accumulated_audio = []
        logger.debug(f"Stopped accumulating audio, {len(result)} chunks collected")
        return result
    
    def add_audio_chunk(self, audio_chunk):
        """
        Add an audio chunk to the accumulation buffer.
        
        Args:
            audio_chunk: Audio data as a numpy array
            
        Returns:
            True if successful, False otherwise
        """
        if self.is_accumulating:
            try:
                self.accumulated_audio.append(audio_chunk.copy())
                return True
            except Exception as e:
                logger.error(f"Error adding audio chunk: {e}")
                return False
        else:
            logger.warning("Not accumulating audio, chunk ignored")
            return False
    
    def _convert_audio_chunks_to_wav(self, audio_chunks, sample_rate=16000, channels=1):
        """
        Convert a list of audio chunks to a temporary WAV file.
        
        Args:
            audio_chunks: List of audio chunks as numpy arrays
            sample_rate: Sample rate of the audio
            channels: Number of audio channels
            
        Returns:
            Path to the temporary WAV file
        """
        if not audio_chunks:
            raise ValueError("No audio chunks provided")
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
        temp_path = temp_file.name
        temp_file.close()
        
        try:
            # Check the dtype of the first chunk to determine the sample width
            if audio_chunks[0].dtype == np.int16:
                sample_width = 2  # 16-bit
            elif audio_chunks[0].dtype == np.int32:
                sample_width = 4  # 32-bit
            elif audio_chunks[0].dtype == np.float32:
                # Convert float32 to int16
                audio_chunks = [np.int16(chunk * 32767) for chunk in audio_chunks]
                sample_width = 2  # 16-bit
            else:
                raise ValueError(f"Unsupported audio dtype: {audio_chunks[0].dtype}")
            
            # Concatenate all chunks into a single array
            audio_data = np.concatenate(audio_chunks)
            
            # Write the WAV file
            with wave.open(temp_path, 'wb') as wav_file:
                wav_file.setnchannels(channels)
                wav_file.setsampwidth(sample_width)
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())
            
            return temp_path
        except Exception as e:
            logger.error(f"Error converting audio chunks to WAV: {e}")
            try:
                os.unlink(temp_path)
            except Exception:
                pass
            raise
    
    def transcribe(self, audio_data, lang_code=None):
        """
        Transcribe audio data using faster-whisper.
        
        Args:
            audio_data: Audio data as a numpy array, list of numpy arrays, or path to WAV file
            lang_code: Language code (defaults to active model language)
            
        Returns:
            Transcribed text or empty string if transcription failed
        """
        # Use active model language if none specified
        if lang_code is None:
            lang_code = self.active_model_lang
            logger.debug(f"No lang_code provided to transcribe, using active_model_lang: {lang_code}")
        
        if lang_code is None:
            logger.warning("No active model language specified for transcription.")
            return ""
        
        logger.info(f"Attempting to transcribe with lang_code: {lang_code}")

        if lang_code not in self.models:
            logger.warning(f"Model for {lang_code} not loaded. Attempting to load.")
            loaded = self.load_model(lang_code)
            if not loaded:
                return ""
        
        try:
            model = self.models[lang_code]
            
            # Handle different types of audio_data
            temp_file_path = None
            
            if isinstance(audio_data, str) and os.path.exists(audio_data):
                # Audio data is already a file path
                audio_file_path = audio_data
            elif isinstance(audio_data, list) and all(isinstance(chunk, np.ndarray) for chunk in audio_data):
                # Convert list of numpy arrays to temporary WAV file
                temp_file_path = self._convert_audio_chunks_to_wav(audio_data)
                audio_file_path = temp_file_path
            elif isinstance(audio_data, np.ndarray):
                # Convert single numpy array to temporary WAV file
                temp_file_path = self._convert_audio_chunks_to_wav([audio_data])
                audio_file_path = temp_file_path
            else:
                logger.error(f"Unsupported audio data type: {type(audio_data)}")
                return ""
            
            # Transcribe the audio
            logger.info(f"Transcribing audio with {lang_code} model using device: {self.device} and compute_type: {self.compute_type}")
            segments, info = model.transcribe(
                audio_file_path,
                language=lang_code,
                beam_size=5,
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=500)
            )
            
            # Collect segments
            transcription = ""
            for segment in segments:
                transcription += segment.text + " "
            
            transcription = transcription.strip()
            
            logger.info(f"Transcription complete. Detected language: {info.language} ({info.language_probability:.2f})")
            logger.debug(f"Transcribed text: {transcription[:100]}{'...' if len(transcription) > 100 else ''}")
            
            return transcription
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            return ""
        finally:
            # Clean up temporary file if created
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    logger.warning(f"Failed to delete temporary file {temp_file_path}: {e}")
    
    def transcribe_accumulated(self, audio_data, lang_code=None):
        """
        Transcribe accumulated audio data.
        
        Args:
            audio_data: List of audio chunks (numpy arrays) to transcribe.
            lang_code: Language code (defaults to active model language)
            
        Returns:
            Transcribed text or empty string if transcription failed
        """
        if not audio_data:
            logger.warning("No accumulated audio to transcribe")
            return ""
        
        try:
            # Use the provided audio_data instead of self.accumulated_audio
            return self.transcribe(audio_data, lang_code)
        except Exception as e:
            logger.error(f"Error transcribing accumulated audio: {e}")
            return ""
    
    def get_supported_languages(self):
        """
        Get the list of supported languages.
        
        Returns:
            List of supported language codes
        """
        return self.supported_languages
    
    def get_active_language(self):
        """
        Get the active language.
        
        Returns:
            Active language code or None if no language is active
        """
        return self.active_model_lang
    
    def set_active_language(self, lang_code):
        """
        Set the active language.
        
        Args:
            lang_code: Language code
            
        Returns:
            True if successful, False otherwise
        """
        if lang_code not in self.supported_languages:
            logger.warning(f"Unsupported language: {lang_code}")
            return False
        
        if lang_code not in self.models:
            loaded = self.load_model(lang_code)
            if not loaded:
                return False
        
        self.active_model_lang = lang_code
        logger.info(f"Active language set to {lang_code}")
        return True 