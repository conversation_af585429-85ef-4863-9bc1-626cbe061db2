"""
Audio handler for TalktoWrite.

This module handles audio recording from the microphone and provides audio chunks for processing.
"""

import logging
import queue
import numpy as np
import sounddevice as sd
import threading

# Setup logging
logger = logging.getLogger(__name__)

class AudioHandler:
    """Handles audio recording from the microphone."""

    def __init__(self, sample_rate=16000, channels=1, dtype='int16', blocksize=1024):
        """
        Initialize the audio handler.
        
        Args:
            sample_rate: Sample rate in Hz
            channels: Number of audio channels (1 for mono, 2 for stereo)
            dtype: Data type for audio samples ('int16' or 'float32')
            blocksize: Number of frames per block
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.dtype = dtype
        self.blocksize = blocksize
        self.audio_queue = queue.Queue()
        self.is_recording = False
        self.stream = None
        self.lock = threading.Lock()
        
        # Try to query the default input device
        try:
            device_info = sd.query_devices(kind='input')
            logger.info(f"Default input device: {device_info['name']}")
        except Exception as e:
            logger.warning(f"Could not query default input device: {e}")

    def _audio_callback(self, indata, frames, time, status):
        """
        Callback function for the InputStream.
        
        Args:
            indata: Input audio data as a numpy array
            frames: Number of frames
            time: Stream time
            status: Status flag
        """
        if status:
            logger.warning(f"Audio callback status: {status}")
        
        try:
            # Make a copy of the data to ensure it's not changed by sounddevice
            data = indata.copy()
            self.audio_queue.put(data)
        except Exception as e:
            logger.error(f"Error in audio callback: {e}")

    def start_recording(self):
        """Start recording audio from the microphone."""
        with self.lock:
            if not self.is_recording:
                try:
                    self.stream = sd.InputStream(
                        samplerate=self.sample_rate,
                        channels=self.channels,
                        dtype=self.dtype,
                        blocksize=self.blocksize,
                        callback=self._audio_callback
                    )
                    self.stream.start()
                    self.is_recording = True
                    logger.info("Audio recording started")
                except Exception as e:
                    logger.error(f"Error starting audio recording: {e}")
                    self.is_recording = False

    def stop_recording(self):
        """Stop recording audio from the microphone."""
        with self.lock:
            if self.is_recording and self.stream:
                try:
                    self.stream.stop()
                    self.stream.close()
                    self.stream = None
                    self.is_recording = False
                    logger.info("Audio recording stopped")
                except Exception as e:
                    logger.error(f"Error stopping audio recording: {e}")

    def get_audio_chunk(self, block=False, timeout=None):
        """
        Get a chunk of audio data from the queue.
        
        Args:
            block: If True, block until audio data is available or timeout occurs
            timeout: Maximum time to block (in seconds)
            
        Returns:
            Audio data as a numpy array if available, None otherwise
        """
        try:
            if block:
                return self.audio_queue.get(block=True, timeout=timeout)
            else:
                return self.audio_queue.get_nowait() if not self.audio_queue.empty() else None
        except queue.Empty:
            return None
        except Exception as e:
            logger.error(f"Error getting audio chunk: {e}")
            return None

    def clear_queue(self):
        """Clear the audio queue."""
        try:
            while not self.audio_queue.empty():
                self.audio_queue.get_nowait()
            logger.debug("Audio queue cleared")
        except Exception as e:
            logger.error(f"Error clearing audio queue: {e}")

    def is_active(self):
        """Check if recording is active."""
        with self.lock:
            return self.is_recording 