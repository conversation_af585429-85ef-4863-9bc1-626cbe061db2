"""
Settings GUI for TalktoWrite.

This module provides a graphical interface for configuring the application.
"""

import logging
import threading
import customtkinter as ctk
from src.config_manager import ConfigManager

# Setup logging
logger = logging.getLogger(__name__)

class SettingsGUI:
    """GUI for configuring TalktoWrite settings."""
    
    def __init__(self, config_manager=None):
        """
        Initialize the settings GUI.
        
        Args:
            config_manager: ConfigManager instance to use (creates a new one if None)
        """
        self.config_manager = config_manager or ConfigManager()
        self.window = None
        self.hotkey_var = None
        self.language_var = None
        self.is_listening_hotkey = False
        self.key_listener_thread = None
        self.stop_listening = threading.Event()
    
    def show(self):
        """Show the settings window."""
        if self.window is not None:
            self.window.deiconify()
            self.window.lift()
            return
        
        # Configure customtkinter
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create window
        self.window = ctk.CTk()
        self.window.title("TalktoWrite Settings")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # Create main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="TalktoWrite Settings",
            font=("Helvetica", 20, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Hotkey setting
        hotkey_frame = ctk.CTkFrame(main_frame)
        hotkey_frame.pack(fill="x", padx=10, pady=10)
        
        hotkey_label = ctk.CTkLabel(
            hotkey_frame,
            text="Recording Hotkey:",
            font=("Helvetica", 14)
        )
        hotkey_label.pack(side="left", padx=(10, 0))
        
        self.hotkey_var = ctk.StringVar(value=self.config_manager.get_hotkey())
        hotkey_entry = ctk.CTkEntry(
            hotkey_frame,
            textvariable=self.hotkey_var,
            width=200,
            state="readonly"
        )
        hotkey_entry.pack(side="left", padx=10)
        
        hotkey_button = ctk.CTkButton(
            hotkey_frame,
            text="Set Hotkey",
            command=self._start_hotkey_listening
        )
        hotkey_button.pack(side="left", padx=10)
        
        # Language setting
        language_frame = ctk.CTkFrame(main_frame)
        language_frame.pack(fill="x", padx=10, pady=10)
        
        language_label = ctk.CTkLabel(
            language_frame,
            text="Transcription Language:",
            font=("Helvetica", 14)
        )
        language_label.pack(side="left", padx=(10, 0))
        
        self.language_var = ctk.StringVar(value=self.config_manager.get_active_language())
        language_option_menu = ctk.CTkOptionMenu(
            language_frame,
            values=["en", "it", "ro"],
            variable=self.language_var,
            command=self._on_language_change
        )
        language_option_menu.pack(side="left", padx=10)
        
        # Language descriptions
        language_desc_frame = ctk.CTkFrame(main_frame)
        language_desc_frame.pack(fill="x", padx=10, pady=10)
        
        language_desc_label = ctk.CTkLabel(
            language_desc_frame,
            text="Language Codes:",
            font=("Helvetica", 14)
        )
        language_desc_label.pack(anchor="w", padx=10, pady=(0, 5))
        
        language_desc_en = ctk.CTkLabel(
            language_desc_frame,
            text="en - English",
            font=("Helvetica", 12)
        )
        language_desc_en.pack(anchor="w", padx=20)
        
        language_desc_it = ctk.CTkLabel(
            language_desc_frame,
            text="it - Italian (Italiano)",
            font=("Helvetica", 12)
        )
        language_desc_it.pack(anchor="w", padx=20)
        
        language_desc_ro = ctk.CTkLabel(
            language_desc_frame,
            text="ro - Romanian (Română)",
            font=("Helvetica", 12)
        )
        language_desc_ro.pack(anchor="w", padx=20)
        
        # Notification position
        position_frame = ctk.CTkFrame(main_frame)
        position_frame.pack(fill="x", padx=10, pady=10)
        
        position_label = ctk.CTkLabel(
            position_frame,
            text="Notification Position:",
            font=("Helvetica", 14)
        )
        position_label.pack(anchor="w", padx=10, pady=(0, 5))
        
        # Get current position
        notification_pos = self.config_manager.get_config("notification_position", {"x": 10, "y": 10})
        
        # X position
        x_frame = ctk.CTkFrame(position_frame)
        x_frame.pack(fill="x", padx=20, pady=5)
        
        x_label = ctk.CTkLabel(
            x_frame,
            text="X:",
            width=30
        )
        x_label.pack(side="left", padx=(0, 5))
        
        self.x_var = ctk.IntVar(value=notification_pos["x"])
        x_slider = ctk.CTkSlider(
            x_frame,
            from_=0,
            to=1920,
            variable=self.x_var,
            command=self._on_position_change
        )
        x_slider.pack(side="left", fill="x", expand=True, padx=5)
        
        x_value_label = ctk.CTkLabel(
            x_frame,
            textvariable=self.x_var,
            width=50
        )
        x_value_label.pack(side="left", padx=5)
        
        # Y position
        y_frame = ctk.CTkFrame(position_frame)
        y_frame.pack(fill="x", padx=20, pady=5)
        
        y_label = ctk.CTkLabel(
            y_frame,
            text="Y:",
            width=30
        )
        y_label.pack(side="left", padx=(0, 5))
        
        self.y_var = ctk.IntVar(value=notification_pos["y"])
        y_slider = ctk.CTkSlider(
            y_frame,
            from_=0,
            to=1080,
            variable=self.y_var,
            command=self._on_position_change
        )
        y_slider.pack(side="left", fill="x", expand=True, padx=5)
        
        y_value_label = ctk.CTkLabel(
            y_frame,
            textvariable=self.y_var,
            width=50
        )
        y_value_label.pack(side="left", padx=5)
        
        # Status label
        self.status_var = ctk.StringVar(value="")
        status_label = ctk.CTkLabel(
            main_frame,
            textvariable=self.status_var,
            font=("Helvetica", 12, "italic")
        )
        status_label.pack(pady=10)
        
        # Save and close buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        save_button = ctk.CTkButton(
            button_frame,
            text="Save Settings",
            command=self._save_settings
        )
        save_button.pack(side="left", padx=10, expand=True)
        
        close_button = ctk.CTkButton(
            button_frame,
            text="Close",
            command=self._on_close
        )
        close_button.pack(side="left", padx=10, expand=True)
        
        # Center window on screen
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
        # Show window
        self.window.mainloop()
    
    def _on_close(self):
        """Handle window close event."""
        if self.is_listening_hotkey:
            self.stop_listening.set()
            if self.key_listener_thread and self.key_listener_thread.is_alive():
                self.key_listener_thread.join(timeout=1.0)
        
        self.window.destroy()
        self.window = None
    
    def _start_hotkey_listening(self):
        """Start listening for a new hotkey."""
        if self.is_listening_hotkey:
            return
        
        self.status_var.set("Press a key combination (e.g., Ctrl+Alt+R)...")
        self.is_listening_hotkey = True
        self.stop_listening.clear()
        
        self.key_listener_thread = threading.Thread(target=self._listen_for_hotkey)
        self.key_listener_thread.daemon = True
        self.key_listener_thread.start()
    
    def _listen_for_hotkey(self):
        """Listen for a new hotkey combination."""
        try:
            import pynput.keyboard as keyboard
            
            pressed_keys = set()
            key_names = []
            
            def on_press(key):
                try:
                    # Convert key to string representation
                    if hasattr(key, 'char') and key.char:
                        key_str = key.char.lower()
                    elif hasattr(key, 'name'):
                        key_str = f"<{key.name.lower()}>"
                    else:
                        key_str = str(key).lower().replace("key.", "<").replace(">", "").replace("'", "") + ">"
                    
                    if key_str not in pressed_keys:
                        pressed_keys.add(key_str)
                        key_names.append(key_str)
                        
                        # Update display
                        hotkey_str = '+'.join(key_names)
                        self.hotkey_var.set(hotkey_str)
                        
                        # If we have at least 2 keys, consider it a valid hotkey
                        if len(key_names) >= 2:
                            # Stop listening after a short delay
                            self.window.after(500, self._stop_hotkey_listening)
                except Exception as e:
                    logger.error(f"Error in hotkey listener: {e}")
            
            def on_release(key):
                # For simplicity, we don't handle key releases specially
                pass
            
            # Start keyboard listener
            with keyboard.Listener(on_press=on_press, on_release=on_release) as listener:
                while not self.stop_listening.is_set() and listener.running:
                    self.stop_listening.wait(0.1)
                listener.stop()
        
        except Exception as e:
            logger.error(f"Error in hotkey listener thread: {e}")
        finally:
            self.is_listening_hotkey = False
    
    def _stop_hotkey_listening(self):
        """Stop listening for a new hotkey."""
        if not self.is_listening_hotkey:
            return
        
        self.stop_listening.set()
        self.status_var.set(f"Hotkey set to: {self.hotkey_var.get()}")
        self.is_listening_hotkey = False
    
    def _on_language_change(self, value):
        """Handle language change."""
        self.status_var.set(f"Language set to: {value}")
    
    def _on_position_change(self, value):
        """Handle notification position change."""
        # This is called during slider movement, so we don't save immediately
        pass
    
    def _save_settings(self):
        """Save all settings."""
        try:
            # Save hotkey
            self.config_manager.set_hotkey(self.hotkey_var.get())
            
            # Save language
            self.config_manager.set_active_language(self.language_var.get())
            
            # Save notification position
            self.config_manager.set_config("notification_position", {
                "x": self.x_var.get(),
                "y": self.y_var.get()
            })
            
            self.status_var.set("Settings saved successfully!")
            logger.info("Settings saved via GUI")
        except Exception as e:
            self.status_var.set(f"Error saving settings: {e}")
            logger.error(f"Error saving settings: {e}")

def main():
    """Run the settings GUI as a standalone application."""
    settings_gui = SettingsGUI()
    settings_gui.show()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main() 