"""
Hotkey listener for TalktoWrite.

This module handles global hotkey registration and event management.
"""

import logging
import threading
import queue
import platform
import os
from pynput import keyboard

# Setup logging
logger = logging.getLogger(__name__)

class HotkeyListener:
    """Listens for global hotkeys and triggers events."""

    def __init__(self, hotkey_str):
        """
        Initialize the hotkey listener.
        
        Args:
            hotkey_str: A string representation of the hotkey (e.g., '<ctrl>+<alt>+r')
        """
        self.hotkey_str = hotkey_str
        self.event_queue = queue.Queue()
        self.listener = None
        self.is_active = False
        self.debug_mode = False    # Debug mode flag
        self._parsed_hotkey_combo = [] # Store the parsed combination for HotKey class
        self._setup_hotkey()
        self._check_environment()

    def _check_environment(self):
        """Check the environment for potential issues with hotkey listener."""
        logger.debug(f"Platform: {platform.system()} {platform.release()}")
        
        if platform.system() == "Windows":
            from ctypes import windll
            # Check keyboard layout
            layout_id = windll.user32.GetKeyboardLayout(0) & 0xFFFF
            logger.debug(f"Keyboard layout ID: {layout_id:04x}")
            
            # Common layout IDs:
            # 0x0409 - US English
            # 0x0410 - Italian
            # 0x0411 - Japanese
            # 0x0407 - German
            # 0x040c - French
            
            # Check if Admin privileges might be needed
            try:
                import ctypes
                is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
                logger.debug(f"Running with admin privileges: {is_admin}")
                if not is_admin:
                    logger.warning("Application is not running with admin privileges, which might affect global hotkey registration")
            except Exception as e:
                logger.warning(f"Could not check admin privileges: {e}")

    def _setup_hotkey(self):
        """Set up the hotkey combination."""
        try:
            # Parse the hotkey string into a list of keys
            parsed_keys = []
            parts = self.hotkey_str.split('+')
            logger.debug(f"Parsing hotkey parts: {parts}")
            
            current_keys_for_hotkey_class = []
            for key_str in parts:
                key_str = key_str.strip().lower()
                
                # Handle special key formats like <ctrl>, <alt>, etc.
                if key_str.startswith('<') and key_str.endswith('>'):
                    key_name = key_str[1:-1]  # Remove < and >
                    if hasattr(keyboard.Key, key_name):
                        key = getattr(keyboard.Key, key_name)
                    else:
                        logger.warning(f"Unknown special key: {key_name}, attempting to treat as char.")
                        # Fallback for unknown special keys, treat as char if possible
                        if len(key_name) == 1:
                            key = keyboard.KeyCode.from_char(key_name)
                        else:
                            logger.error(f"Cannot interpret special key: {key_name}")
                            # Potentially raise an error or handle more gracefully
                            raise ValueError(f"Unknown special key: {key_name}")
                else:
                    # Regular character key
                    if len(key_str) == 1:
                        key = keyboard.KeyCode.from_char(key_str)
                    else:
                        logger.error(f"Cannot interpret multi-character key part as a single key: {key_str}")
                        raise ValueError(f"Invalid key part: {key_str}")

                parsed_keys.append(key)
                logger.debug(f"Added key to combination: {key}")
            
            self._parsed_hotkey_combo = parsed_keys # Store for pynput.HotKey
            self.hotkey = keyboard.HotKey(self._parsed_hotkey_combo, self._on_activate)
            logger.info(f"Hotkey configured: {self.hotkey_str} with parsed keys: {self._parsed_hotkey_combo}")
        except Exception as e:
            logger.error(f"Error setting up hotkey: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _on_activate(self):
        """
        Callback function when the hotkey is pressed.
        
        This function is executed in the context of the system thread
        and should be very lightweight to avoid blocking user input.
        """
        try:
            # Put a simple message in the queue and return immediately
            # to avoid blocking the input system
            logger.info("HOTKEY ACTIVATED - Combination detected!")
            self.event_queue.put("HOTKEY_PRESSED")
            logger.debug("Hotkey activation event queued")
        except Exception as e:
            logger.error(f"Error in hotkey callback: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _on_press(self, key):
        """Process key press events."""
        try:
            # In debug mode, log all keypresses prominently
            if self.debug_mode:
                logger.warning(f"DEBUG - Key pressed: {key} (canonical: {self.listener.canonical(key) if self.listener else key})")
            else:
                logger.debug(f"Key pressed: {key} (canonical: {self.listener.canonical(key) if self.listener else key})")
            
            if self.listener: # Ensure listener is available
                self.hotkey.press(self.listener.canonical(key))
            else:
                logger.warning("Listener not available in _on_press")

        except Exception as e:
            logger.error(f"Error processing key press: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _on_release(self, key):
        """Process key release events."""
        try:
            if self.debug_mode:
                logger.warning(f"DEBUG - Key released: {key} (canonical: {self.listener.canonical(key) if self.listener else key})")
            else:
                logger.debug(f"Key released: {key} (canonical: {self.listener.canonical(key) if self.listener else key})")

            if self.listener: # Ensure listener is available
                self.hotkey.release(self.listener.canonical(key))
            else:
                logger.warning("Listener not available in _on_release")

        except Exception as e:
            logger.error(f"Error processing key release: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def enable_debug_mode(self):
        """Enable keyboard debug mode to log all key presses more prominently."""
        self.debug_mode = True
        logger.warning("Keyboard debug mode ENABLED - All key presses will be logged prominently")
        
    def disable_debug_mode(self):
        """Disable keyboard debug mode."""
        self.debug_mode = False
        logger.info("Keyboard debug mode disabled")

    def test_keyboard_connection(self):
        """
        Test whether the keyboard listener is working.
        This creates a separate listener just for testing purposes.
        """
        logger.info("Starting keyboard connection test... Press any key to test (ESC to end test)")
        
        test_results = {"keys_detected": 0, "success": False}
        test_event = threading.Event()
        
        def on_test_press(key):
            test_results["keys_detected"] += 1
            logger.warning(f"TEST - Key detected: {key}")
            
            if key == keyboard.Key.esc:
                logger.info("Keyboard test completed - ESC pressed")
                test_event.set()
                return False
        
        try:
            # Create a temporary listener just for testing
            test_listener = keyboard.Listener(on_press=on_test_press)
            test_listener.start()
            
            # Wait for the test to complete (ESC pressed or timeout)
            test_completed = test_event.wait(timeout=10.0)
            
            if test_completed:
                test_results["success"] = True
                logger.info(f"Keyboard test PASSED - {test_results['keys_detected']} keys detected")
            else:
                if test_results["keys_detected"] > 0:
                    test_results["success"] = True
                    logger.info(f"Keyboard test PASSED - {test_results['keys_detected']} keys detected (timed out)")
                else:
                    logger.warning("Keyboard test FAILED - No keys detected within timeout period")
            
            # Stop the test listener
            test_listener.stop()
            
            return test_results
            
        except Exception as e:
            logger.error(f"Error during keyboard test: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"keys_detected": 0, "success": False, "error": str(e)}

    def start(self):
        """Start listening for the hotkey."""
        if not self.is_active:
            try:
                logger.debug(f"Starting listener for hotkey: {self.hotkey_str}")
                
                # Create the appropriate listener based on platform
                if platform.system() == "Windows":
                    try:
                        # On Windows, use the default listener without custom event filter
                        self.listener = keyboard.Listener(
                            on_press=self._on_press,
                            on_release=self._on_release
                        )
                        logger.debug("Created standard keyboard listener for Windows")
                    except Exception as e:
                        logger.error(f"Error creating keyboard listener: {e}")
                        return False
                else:
                    # Default listener for other platforms
                    self.listener = keyboard.Listener(
                        on_press=self._on_press,
                        on_release=self._on_release
                    )
                
                self.listener.start()
                self.is_active = True
                logger.info(f"Hotkey listener started successfully. Listening for: {self.hotkey_str}")
                
                # Verify listener is running
                if not self.listener.is_alive():
                    logger.error("Listener thread is not alive after starting!")
                    self.is_active = False
                    return False
                
                return True
            except Exception as e:
                logger.error(f"Error starting hotkey listener: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                self.is_active = False
                return False
        
        return True

    def stop(self):
        """Stop listening for the hotkey."""
        if self.is_active and self.listener:
            try:
                self.listener.stop()
                self.listener = None
                self.is_active = False
                logger.info("Hotkey listener stopped")
            except Exception as e:
                logger.error(f"Error stopping hotkey listener: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")

    def check_event(self, block=False, timeout=None):
        """
        Check if a hotkey event has occurred.
        
        Args:
            block: If True, block until an event is available or timeout occurs
            timeout: Maximum time to block (in seconds)
            
        Returns:
            Event string if available, None otherwise
        """
        try:
            if block:
                result = self.event_queue.get(block=True, timeout=timeout)
                if result:
                    logger.debug(f"Retrieved hotkey event from queue: {result}")
                return result
            else:
                if not self.event_queue.empty():
                    result = self.event_queue.get_nowait()
                    logger.debug(f"Retrieved hotkey event from queue: {result}")
                    return result
                return None
        except queue.Empty:
            return None
        except Exception as e:
            logger.error(f"Error checking hotkey events: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def update_hotkey(self, new_hotkey_str):
        """
        Update the hotkey to a new combination.
        
        Args:
            new_hotkey_str: A string representation of the new hotkey
        """
        was_active = self.is_active
        if was_active:
            self.stop()
        
        self.hotkey_str = new_hotkey_str
        self._setup_hotkey()
        
        if was_active:
            self.start()
        
        logger.info(f"Hotkey updated to: {new_hotkey_str}") 