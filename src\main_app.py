"""
Main application for TalktoWrite.

This module orchestrates all components of the application.
"""

import logging
import threading
import time
import sys
import signal
import argparse
import traceback

from src.config_manager import ConfigManager
from src.hotkey_listener import HotkeyListener
from src.audio_handler import AudioHandler
from src.notification_manager import NotificationManager
from src.text_injector import TextInjector
from src.stt_engine_wrapper import STTEngineWrapper
from src.settings_gui import SettingsGUI

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,  # Changed from INFO to DEBUG for more verbose output
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("talktowrite.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TalkToWriteApp:
    """Main application class for TalkToWrite."""
    
    def __init__(self):
        """Initialize the application."""
        logger.info("Initializing TalkToWrite application")
        
        try:
            # Load configuration
            self.config_manager = ConfigManager()
            
            # Get hotkey from config
            hotkey = self.config_manager.get_hotkey()
            logger.debug(f"Configured hotkey from settings: {hotkey}")
            
            # Initialize components
            self.hotkey_listener = HotkeyListener(hotkey)
            self.hotkey_listener.enable_debug_mode()
            
            logger.debug("Initializing audio handler")
            self.audio_handler = AudioHandler()
            
            # Get notification position from config
            notification_pos = self.config_manager.get_config("notification_position", {"x": 10, "y": 10})
            logger.debug(f"Notification position: {notification_pos}")
            self.notification_manager = NotificationManager(
                x_pos=notification_pos["x"],
                y_pos=notification_pos["y"]
            )
            
            logger.debug("Initializing text injector")
            self.text_injector = TextInjector()
            
            logger.debug("Initializing STT engine")
            self.stt_engine = STTEngineWrapper()
            
            # Set active language from config
            active_lang = self.config_manager.get_active_language()
            logger.debug(f"Setting active language to: {active_lang}")
            self.stt_engine.set_active_language(active_lang)
            
            # State flags
            self.is_running = False
            self.is_recording = False
            
            # For controlling the main loop
            self.stop_event = threading.Event()
            
            # Main thread
            self.main_thread = None
            
            # Settings GUI
            self.settings_gui = None
            
            # Hotkey debounce
            self.last_hotkey_time = 0
            self.hotkey_debounce_period = 1.0 # 1 second debounce
            
            logger.debug("TalkToWrite application initialization complete")
        except Exception as e:
            logger.error(f"Error during initialization: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    def start(self):
        """Start the application."""
        if self.is_running:
            logger.warning("Application already running")
            return
        
        try:
            logger.info("Starting TalkToWrite application")
            
            # Start hotkey listener
            logger.debug("Starting hotkey listener")
            self.hotkey_listener.start()
            
            # Start main thread
            self.is_running = True
            logger.debug("Creating main thread")
            self.main_thread = threading.Thread(target=self._main_loop)
            self.main_thread.daemon = True
            self.main_thread.start()
            
            logger.info("TalkToWrite application started")
        except Exception as e:
            logger.error(f"Error starting application: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.stop()
    
    def enable_keyboard_debug(self):
        """Enable keyboard debug mode."""
        if hasattr(self, 'hotkey_listener') and self.hotkey_listener:
            self.hotkey_listener.enable_debug_mode()
            logger.info("Keyboard debug mode enabled")
    
    def test_keyboard(self):
        """Run a keyboard test."""
        if hasattr(self, 'hotkey_listener') and self.hotkey_listener:
            logger.info("Starting keyboard test...")
            results = self.hotkey_listener.test_keyboard_connection()
            logger.info(f"Keyboard test results: {results}")
            return results
        else:
            logger.error("Hotkey listener not initialized, cannot run test")
            return {"success": False, "error": "Hotkey listener not initialized"}
    
    def stop(self):
        """Stop the application."""
        if not self.is_running:
            return
        
        try:
            logger.info("Stopping TalkToWrite application")
            
            # Signal main loop to stop
            logger.debug("Setting stop event")
            self.stop_event.set()
            
            # Stop recording if active
            if self.is_recording:
                logger.debug("Recording was active, stopping recording")
                self._stop_recording()
            
            # Stop hotkey listener
            logger.debug("Stopping hotkey listener")
            self.hotkey_listener.stop()
            
            # Wait for main thread to finish
            if self.main_thread and self.main_thread.is_alive():
                logger.debug("Waiting for main thread to finish")
                self.main_thread.join(timeout=2.0)
            
            # Close notification window
            logger.debug("Closing notification window")
            self.notification_manager.close()
            
            self.is_running = False
            logger.info("TalkToWrite application stopped")
        except Exception as e:
            logger.error(f"Error stopping application: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    def _main_loop(self):
        """Main application loop."""
        logger.info("Main loop started")
        loop_count = 0
        last_status_log = time.time()
        
        try:
            while not self.stop_event.is_set():
                loop_count += 1
                
                # Log status every 30 seconds
                current_time = time.time()
                if current_time - last_status_log >= 30:
                    logger.debug(f"Main loop alive - iteration {loop_count}. Recording: {self.is_recording}")
                    last_status_log = current_time
                
                # Check for hotkey events
                logger.debug("Checking for hotkey events")
                event = self.hotkey_listener.check_event(block=True, timeout=0.1)
                
                if event == "HOTKEY_PRESSED":
                    current_time_event = time.time()
                    if (current_time_event - self.last_hotkey_time) > self.hotkey_debounce_period:
                        logger.info("Hotkey event received in main loop - toggling recording state")
                        self._toggle_recording()
                        self.last_hotkey_time = current_time_event
                    else:
                        logger.debug("Hotkey event debounced/ignored due to quick succession")
                
                # If recording, process audio chunks
                if self.is_recording:
                    self._process_audio()
                
                # Small sleep to prevent tight loop
                time.sleep(0.01)
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
        
        logger.info("Main loop ended")
    
    def _toggle_recording(self):
        """Toggle recording state."""
        logger.debug(f"Toggling recording state. Current state: {self.is_recording}")
        if self.is_recording:
            self._stop_recording()
        else:
            self._start_recording()
    
    def _start_recording(self):
        """Start the recording process."""
        if self.is_recording:
            logger.debug("Recording already active, ignoring start request")
            return
        
        try:
            logger.info("Starting recording")
            
            # Start audio recording
            logger.debug("Starting audio handler recording")
            self.audio_handler.start_recording()
            
            # Start accumulating audio in the STT engine
            logger.debug("Starting STT engine audio accumulation")
            self.stt_engine.start_accumulating()
            
            # Show recording notification
            logger.debug("Showing recording notification")
            self.notification_manager.show()
            
            self.is_recording = True
            logger.info("Recording started successfully")
        except Exception as e:
            logger.error(f"Error starting recording: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            # Try to clean up
            logger.debug("Attempting cleanup after recording start failure")
            self.audio_handler.stop_recording()
            self.notification_manager.hide()
            self.is_recording = False
    
    def _stop_recording(self):
        """Stop the recording process and process the audio."""
        if not self.is_recording:
            logger.debug("Recording not active, ignoring stop request")
            return
        
        try:
            logger.info("Stopping recording")
            
            # Stop audio recording
            logger.debug("Stopping audio handler recording")
            self.audio_handler.stop_recording()
            
            # Hide recording notification
            logger.debug("Hiding recording notification")
            self.notification_manager.hide()
            
            # Get accumulated audio and transcribe
            logger.debug("Stopping STT engine audio accumulation")
            audio_data = self.stt_engine.stop_accumulating()
            
            # Transcribe the audio
            if audio_data:
                logger.debug(f"Transcribing {len(audio_data)} accumulated audio chunks")
                text = self.stt_engine.transcribe_accumulated(audio_data, lang_code=self.stt_engine.get_active_language())
                
                if text:
                    logger.info(f"Transcribed text: {text[:30]}{'...' if len(text) > 30 else ''}")
                    
                    # Type the transcribed text
                    logger.debug("Injecting transcribed text")
                    self.text_injector.type_text(text)
                else:
                    logger.warning("No text transcribed from audio")
            else:
                logger.warning("No audio data to transcribe")
            
            self.is_recording = False
            logger.info("Recording stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping recording: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.is_recording = False
    
    def _process_audio(self):
        """Process audio chunks during recording."""
        try:
            # Get audio chunk without blocking
            audio_chunk = self.audio_handler.get_audio_chunk(block=False)
            
            if audio_chunk is not None:
                # Add audio chunk to STT engine
                self.stt_engine.add_audio_chunk(audio_chunk)
        except Exception as e:
            logger.error(f"Error processing audio: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
    
    def show_settings(self):
        """Show the settings GUI."""
        try:
            if self.settings_gui is None:
                self.settings_gui = SettingsGUI(self.config_manager)
            
            # Run the settings GUI in a separate thread
            settings_thread = threading.Thread(target=self._show_settings_thread)
            settings_thread.daemon = True
            settings_thread.start()
        except Exception as e:
            logger.error(f"Error showing settings: {e}")
    
    def _show_settings_thread(self):
        """Run the settings GUI in a separate thread."""
        try:
            self.settings_gui.show()
            
            # After settings window is closed, update components with new settings
            self._apply_settings_changes()
        except Exception as e:
            logger.error(f"Error in settings thread: {e}")
    
    def _apply_settings_changes(self):
        """Apply changes made in the settings GUI."""
        try:
            # Update hotkey
            new_hotkey = self.config_manager.get_hotkey()
            if self.hotkey_listener.hotkey_str != new_hotkey:
                logger.info(f"Updating hotkey to: {new_hotkey}")
                self.hotkey_listener.update_hotkey(new_hotkey)
            
            # Update active language
            new_language = self.config_manager.get_active_language()
            if self.stt_engine.get_active_language() != new_language:
                logger.info(f"Updating active language to: {new_language}")
                self.stt_engine.set_active_language(new_language)
            
            # Update notification position
            new_pos = self.config_manager.get_config("notification_position", {"x": 10, "y": 10})
            self.notification_manager.update_position(new_pos["x"], new_pos["y"])
            
            logger.info("Applied settings changes")
        except Exception as e:
            logger.error(f"Error applying settings changes: {e}")

def signal_handler(sig, frame):
    """Handle system signals."""
    logger.info(f"Received signal {sig}")
    if app:
        app.stop()
    sys.exit(0)

# Global app instance for signal handler
app = None

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="TalktoWrite - AI Voice Transcription Application")
    parser.add_argument("--settings", action="store_true", help="Show settings window")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--keyboard-test", action="store_true", help="Run keyboard test before starting")
    parser.add_argument("--keyboard-debug", action="store_true", help="Enable keyboard debug mode (logs all key presses)")
    return parser.parse_args()

def main():
    """Main entry point."""
    global app
    
    # Parse command line arguments
    args = parse_arguments()
    
    # Set log level
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    try:
        # Register signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Create application
        app = TalkToWriteApp()
        
        # Run keyboard test if requested
        if args.keyboard_test:
            logger.info("Running keyboard test")
            test_results = app.test_keyboard()
            if not test_results.get("success", False):
                logger.error("Keyboard test failed. Application might not work correctly.")
                input("Press Enter to continue anyway or Ctrl+C to abort...")
        
        # Enable keyboard debug mode if requested
        if args.keyboard_debug:
            app.enable_keyboard_debug()
        
        # Show settings if requested
        if args.settings:
            app.show_settings()
        
        # Start application
        app.start()
        
        # Keep main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        if app:
            app.stop()

if __name__ == "__main__":
    main() 