"""
Notification manager for TalktoWrite.

This module handles the visual notification display that indicates recording status.
"""

import logging
import threading
import time
import customtkinter as ctk

# Setup logging
logger = logging.getLogger(__name__)

class NotificationManager:
    """Manages the recording notification display."""
    
    def __init__(self, x_pos=10, y_pos=10):
        """
        Initialize the notification manager.
        
        Args:
            x_pos: X position of the notification window on screen
            y_pos: Y position of the notification window on screen
        """
        self.x_pos = x_pos
        self.y_pos = y_pos
        self.window = None
        self.label = None
        self.is_visible = False
        self.stop_animation = threading.Event()
        self.animation_thread = None
        
        # Animation frames
        self.animation_frames = ["Sto registrando.", "Sto registrando..", "Sto registrando..."]
        self.current_frame = 0
        
        # Configure customtkinter
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

    def _setup_window(self):
        """Set up the notification window."""
        if self.window is None:
            try:
                # Ensure this is called from the main thread if this is the first CTk instance
                # For simplicity, assuming MainApp or another CTk instance will run mainloop if needed
                self.window = ctk.CTkToplevel() # Using CTkToplevel if MainApp might have a CTk root
                # If this is the ONLY ctk window, it might need to be ctk.CTk() and manage its own loop
                # but CTkToplevel is generally safer if other CTk windows exist or might exist.
                # However, if this is the main/only CTk instance, it must be self.window = ctk.CTk()
                # For now, let's try to make it self-contained and robust.
                # A robust way: check if a root exists, if not, create one.
                # This is complex. Let's assume for now that a mainloop is running somewhere if GUI is shown.
                # The error suggests that the thread doing animation is not the one owning the window.

                # Simpler: If this class is always instantiated in the main thread, then _setup_window is fine.
                # The _animate call is the issue.

                # If there's no existing ctk root, create one.
                # This is typically handled by the main application structure.
                # Let's assume main_app.py will handle the ctk.CTk() mainloop if settings_gui is shown.
                # The notification window might need to be a Toplevel if main_app has a root window.
                # For now, if it's a standalone ctk.CTk(), its methods should be called from the thread it was created in.
                
                # If MainApp doesn't manage a CTk root for notifications, NotificationManager has to.
                # This implies _setup_window should be called in the main thread, and show/hide marshalled.
                # The current structure suggests _setup_window is called from show(), which is called from MainApp's thread.
                # The problem is _animate.

                self.window = ctk.CTk() # Let's assume it's a root for now.
                self.window.title("")
                self.window.geometry(f"200x40+{self.x_pos}+{self.y_pos}")
                self.window.attributes('-topmost', True)  # Always on top
                self.window.overrideredirect(True)  # Remove window decorations
                self.window.configure(bg_color="black")
                
                # Make window semi-transparent
                self.window.attributes('-alpha', 0.8)
                
                # Add label for text
                self.label = ctk.CTkLabel(
                    self.window, 
                    text="Sto registrando...",
                    font=("Helvetica", 14),
                    text_color="white"
                )
                self.label.pack(expand=True, fill="both", padx=10, pady=10)
                
                # Make window draggable
                self._make_draggable(self.window)
                
                logger.info("Notification window created")
            except Exception as e:
                logger.error(f"Error setting up notification window: {e}")
                self.window = None

    def _make_draggable(self, window):
        """
        Make the window draggable by mouse.
        
        Args:
            window: The window to make draggable
        """
        def start_drag(event):
            window.x = event.x
            window.y = event.y
            
        def on_drag(event):
            x = window.winfo_x() - window.x + event.x
            y = window.winfo_y() - window.y + event.y
            window.geometry(f"+{x}+{y}")
            # Update position for future uses
            self.x_pos = x
            self.y_pos = y
            
        window.bind("<ButtonPress-1>", start_drag)
        window.bind("<B1-Motion>", on_drag)

    def _animate(self):
        """Run the text animation loop."""
        # This method runs in a separate thread. UI updates must be scheduled on the main GUI thread.
        if self.label and self.window and self.window.winfo_exists():
            try:
                self.current_frame = (self.current_frame + 1) % len(self.animation_frames)
                # Schedule label update on the main Tkinter thread
                self.window.after(0, lambda: self.label.configure(text=self.animation_frames[self.current_frame]))
                # self.window.update_idletasks() # Not needed here, and dangerous from non-main thread
            except Exception as e:
                logger.error(f"Error scheduling animation frame: {e}")

        if not self.stop_animation.is_set() and self.window and self.window.winfo_exists():
            # Schedule the next call to _animate via the Tkinter event loop
            self.window.after(500, self._animate) # 500ms for animation speed

    def show(self):
        """Show the recording notification."""
        if not self.is_visible:
            try:
                # Ensure _setup_window is called in the main thread
                # This is complex if NotificationManager can be called from any thread.
                # Assuming it's called from where CTk can be initialized.
                self._setup_window()
                if self.window:
                    self.stop_animation.clear()
                    self.current_frame = 0
                    
                    # Start animation by scheduling it on the Tkinter event loop
                    # No separate thread needed if we use .after()
                    # self.animation_thread = threading.Thread(target=self._animate, daemon=True)
                    # self.animation_thread.start()
                    self.window.after(0, self._animate) # Start animation loop
                    
                    self.window.deiconify()
                    self.window.update()
                    self.is_visible = True
                    logger.info("Notification shown")
            except Exception as e:
                logger.error(f"Error showing notification: {e}")
                self.is_visible = False

    def hide(self):
        """Hide the recording notification."""
        if self.is_visible and self.window:
            try:
                # Stop animation by setting the event. The animation loop will check this.
                self.stop_animation.set()
                # if self.animation_thread and self.animation_thread.is_alive():
                #     self.animation_thread.join(timeout=1.0) # No longer using a separate thread for animation
                
                if self.window.winfo_exists():
                    self.window.withdraw()
                self.is_visible = False
                logger.info("Notification hidden")
            except Exception as e:
                logger.error(f"Error hiding notification: {e}")

    def close(self):
        """Close and destroy the notification window."""
        try:
            self.stop_animation.set()
            # if self.animation_thread and self.animation_thread.is_alive():
            #     self.animation_thread.join(timeout=1.0)
            
            if self.window:
                # Schedule destroy on main thread to be safe
                self.window.after(0, self.window.destroy)
                self.window = None
                self.label = None
            
            self.is_visible = False
            logger.info("Notification closed")
        except Exception as e:
            logger.error(f"Error closing notification: {e}")

    def update_position(self, x_pos, y_pos):
        """
        Update the position of the notification window.
        
        Args:
            x_pos: New X position
            y_pos: New Y position
        """
        self.x_pos = x_pos
        self.y_pos = y_pos
        
        if self.window:
            try:
                self.window.geometry(f"+{x_pos}+{y_pos}")
                logger.debug(f"Notification position updated: {x_pos}, {y_pos}")
            except Exception as e:
                logger.error(f"Error updating notification position: {e}")

    def is_showing(self):
        """Check if the notification is currently visible."""
        return self.is_visible 