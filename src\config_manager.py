"""
Configuration manager for TalktoWrite.

This module handles loading and saving user configurations such as hotkeys and language settings.
"""

import json
import os
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class ConfigManager:
    """Manages the application configuration."""

    def __init__(self):
        """Initialize the configuration manager."""
        self.app_data_dir = Path(os.getenv('APPDATA')) / "TalktoWrite"
        self.config_file = self.app_data_dir / "config.json"
        self.default_config = {
            "hotkey": "<ctrl>+<alt>+r",
            "active_language": "en",
            "supported_languages": ["en", "it", "ro"],
            "notification_position": {"x": 10, "y": 10}
        }
        self.config = {}
        self._ensure_app_data_dir()
        self.load_config()

    def _ensure_app_data_dir(self):
        """Create the application data directory if it doesn't exist."""
        if not self.app_data_dir.exists():
            self.app_data_dir.mkdir(parents=True)
            logger.info(f"Created application data directory: {self.app_data_dir}")

    def load_config(self):
        """Load the configuration from the config file."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logger.info("Configuration loaded successfully")
            except json.JSONDecodeError:
                logger.error("Failed to parse configuration file. Using defaults.")
                self.config = self.default_config
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
                self.config = self.default_config
        else:
            logger.info("No configuration file found. Using defaults.")
            self.config = self.default_config
            self.save_config()

    def save_config(self):
        """Save the current configuration to the config file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
            logger.info("Configuration saved successfully")
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")

    def get_config(self, key, default=None):
        """Get a configuration value by key."""
        return self.config.get(key, default)

    def set_config(self, key, value):
        """Set a configuration value by key and save the configuration."""
        self.config[key] = value
        self.save_config()

    def get_hotkey(self):
        """Get the configured hotkey."""
        return self.config.get("hotkey", self.default_config["hotkey"])

    def set_hotkey(self, hotkey):
        """Set the hotkey and save the configuration."""
        self.config["hotkey"] = hotkey
        self.save_config()

    def get_active_language(self):
        """Get the active language for transcription."""
        return self.config.get("active_language", self.default_config["active_language"])

    def set_active_language(self, language):
        """Set the active language and save the configuration."""
        if language in self.config.get("supported_languages", self.default_config["supported_languages"]):
            self.config["active_language"] = language
            self.save_config()
        else:
            logger.warning(f"Language {language} not supported") 