"""
Text injector for TalktoWrite.

This module handles inserting transcribed text into the active window.
"""

import logging
import time
import pyautogui
import pygetwindow as gw

# Setup logging
logger = logging.getLogger(__name__)

# Disable pyautogui's failsafe feature
pyautogui.FAILSAFE = False

class TextInjector:
    """<PERSON>les injecting text into the active window."""
    
    def __init__(self, typing_interval=0.01):
        """
        Initialize the text injector.
        
        Args:
            typing_interval: Interval between key presses in seconds
        """
        self.typing_interval = typing_interval
    
    def get_active_window(self):
        """
        Get the active window.
        
        Returns:
            Active window object or None if no window is active
        """
        try:
            active_window = gw.getActiveWindow()
            if active_window:
                logger.debug(f"Active window: {active_window.title}")
                return active_window
            else:
                logger.warning("No active window found")
                return None
        except Exception as e:
            logger.error(f"Error getting active window: {e}")
            return None
    
    def type_text(self, text):
        """
        Type text into the active window.
        
        Args:
            text: Text to type
            
        Returns:
            True if successful, False otherwise
        """
        if not text:
            logger.warning("No text to type")
            return False
        
        active_window = self.get_active_window()
        if not active_window:
            logger.warning("Cannot type text: No active window")
            return False
        
        try:
            # Ensure the window is in focus
            try:
                active_window.activate()
                time.sleep(0.1)  # Small delay to ensure the window is active
            except Exception as e:
                logger.warning(f"Could not activate window: {e}")
            
            # Type the text
            pyautogui.write(text, interval=self.typing_interval)
            logger.info(f"Text typed: {text[:30]}{'...' if len(text) > 30 else ''}")
            return True
        except Exception as e:
            logger.error(f"Error typing text: {e}")
            return False
    
    def type_text_with_punctuation(self, text):
        """
        Type text with proper punctuation handling.
        This method ensures that punctuation marks are properly handled.
        
        Args:
            text: Text to type
            
        Returns:
            True if successful, False otherwise
        """
        return self.type_text(text)  # Basic implementation, can be extended
    
    def press_key(self, key):
        """
        Press a specific key.
        
        Args:
            key: Key to press (e.g., 'enter', 'tab')
            
        Returns:
            True if successful, False otherwise
        """
        try:
            pyautogui.press(key)
            logger.debug(f"Key pressed: {key}")
            return True
        except Exception as e:
            logger.error(f"Error pressing key {key}: {e}")
            return False
    
    def set_typing_interval(self, interval):
        """
        Set the typing interval.
        
        Args:
            interval: New interval between key presses in seconds
        """
        self.typing_interval = interval
        logger.debug(f"Typing interval set to {interval}")
    
    def type_with_delay(self, text, pre_delay=0.5):
        """
        Type text with a delay before starting.
        
        Args:
            text: Text to type
            pre_delay: Delay in seconds before typing starts
            
        Returns:
            True if successful, False otherwise
        """
        try:
            time.sleep(pre_delay)
            return self.type_text(text)
        except Exception as e:
            logger.error(f"Error in type_with_delay: {e}")
            return False 