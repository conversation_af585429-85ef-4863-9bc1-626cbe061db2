Metadata-Version: 2.4
Name: astroid
Version: 3.3.10
Summary: An abstract syntax tree for Python with inference support.
License-Expression: LGPL-2.1-or-later
Project-URL: Bug tracker, https://github.com/pylint-dev/astroid/issues
Project-URL: Discord server, https://discord.gg/Egy6P8AMB5
Project-URL: Docs, https://pylint.readthedocs.io/projects/astroid/en/latest/
Project-URL: Source Code, https://github.com/pylint-dev/astroid
Keywords: abstract syntax tree,python,static code analysis
Classifier: Development Status :: 6 - Mature
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Quality Assurance
Classifier: Topic :: Software Development :: Testing
Requires-Python: >=3.9.0
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: CONTRIBUTORS.txt
Requires-Dist: typing-extensions>=4; python_version < "3.11"
Dynamic: license-file

Astroid
=======

.. image:: https://codecov.io/gh/pylint-dev/astroid/branch/main/graph/badge.svg?token=Buxy4WptLb
    :target: https://codecov.io/gh/pylint-dev/astroid
    :alt: Coverage badge from codecov

.. image:: https://readthedocs.org/projects/astroid/badge/?version=latest
    :target: http://astroid.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation Status

.. image:: https://img.shields.io/badge/code%20style-black-000000.svg
    :target: https://github.com/ambv/black

.. image:: https://results.pre-commit.ci/badge/github/pylint-dev/astroid/main.svg
   :target: https://results.pre-commit.ci/latest/github/pylint-dev/astroid/main
   :alt: pre-commit.ci status

.. |tidelift_logo| image:: https://raw.githubusercontent.com/pylint-dev/astroid/main/doc/media/Tidelift_Logos_RGB_Tidelift_Shorthand_On-White.png
   :width: 200
   :alt: Tidelift

.. list-table::
   :widths: 10 100

   * - |tidelift_logo|
     - Professional support for astroid is available as part of the
       `Tidelift Subscription`_.  Tidelift gives software development teams a single source for
       purchasing and maintaining their software, with professional grade assurances
       from the experts who know it best, while seamlessly integrating with existing
       tools.

.. _Tidelift Subscription: https://tidelift.com/subscription/pkg/pypi-astroid?utm_source=pypi-astroid&utm_medium=referral&utm_campaign=readme



What's this?
------------

The aim of this module is to provide a common base representation of
python source code. It is currently the library powering pylint's capabilities.

It provides a compatible representation which comes from the `_ast`
module.  It rebuilds the tree generated by the builtin _ast module by
recursively walking down the AST and building an extended ast. The new
node classes have additional methods and attributes for different
usages. They include some support for static inference and local name
scopes. Furthermore, astroid can also build partial trees by inspecting living
objects.


Installation
------------

Extract the tarball, jump into the created directory and run::

    pip install .


If you want to do an editable installation, you can run::

    pip install -e .


If you have any questions, please <NAME_EMAIL>
mailing list for support. See
http://mail.python.org/mailman/listinfo/code-quality for subscription
information and archives.

Documentation
-------------
http://astroid.readthedocs.io/en/latest/


Python Versions
---------------

astroid 2.0 is currently available for Python 3 only. If you want Python 2
support, use an older version of astroid (though note that these versions
are no longer supported).

Test
----

Tests are in the 'test' subdirectory. To launch the whole tests suite, you can use
either `tox` or `pytest`::

    tox
    pytest
