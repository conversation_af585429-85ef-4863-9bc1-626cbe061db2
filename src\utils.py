"""
Utility functions for TalktoWrite.

This module contains utility functions used across the application.
"""

import logging
import os
import sys
import time
from pathlib import Path

# Setup logging
logger = logging.getLogger(__name__)

def get_app_dir():
    """
    Get the application directory.
    
    If the application is frozen (e.g., PyInstaller), use sys._MEIPASS.
    Otherwise, use the directory of the current file.
    
    Returns:
        Path to the application directory
    """
    try:
        if getattr(sys, 'frozen', False):
            # We are running in a PyInstaller bundle
            app_dir = Path(sys._MEIPASS)
            logger.debug(f"Running in PyInstaller bundle, app dir: {app_dir}")
        else:
            # We are running in a normal Python environment
            app_dir = Path(__file__).parent.parent
            logger.debug(f"Running in normal Python environment, app dir: {app_dir}")
        
        return app_dir
    except Exception as e:
        logger.error(f"Error getting app directory: {e}")
        # Fall back to current working directory
        return Path(os.getcwd())

def get_resource_path(relative_path):
    """
    Get the absolute path to a resource file.
    
    Args:
        relative_path: Path relative to the application directory
        
    Returns:
        Absolute path to the resource file
    """
    try:
        app_dir = get_app_dir()
        return app_dir / relative_path
    except Exception as e:
        logger.error(f"Error getting resource path: {e}")
        return Path(relative_path)

def format_time(seconds):
    """
    Format time in seconds to a human-readable string.
    
    Args:
        seconds: Time in seconds
        
    Returns:
        Formatted time string (e.g., "1h 2m 3s")
    """
    try:
        hours, remainder = divmod(int(seconds), 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    except Exception as e:
        logger.error(f"Error formatting time: {e}")
        return str(seconds) + "s"

def measure_execution_time(func):
    """
    Decorator to measure the execution time of a function.
    
    Args:
        func: Function to measure
        
    Returns:
        Wrapped function that logs execution time
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        logger.debug(f"Function {func.__name__} executed in {format_time(execution_time)}")
        return result
    return wrapper

def is_admin():
    """
    Check if the application is running with administrator privileges.
    
    Returns:
        True if running as admin, False otherwise
    """
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    except Exception as e:
        logger.error(f"Error checking admin status: {e}")
        return False

def ensure_dir_exists(directory):
    """
    Ensure that a directory exists, creating it if necessary.
    
    Args:
        directory: Directory path
        
    Returns:
        Path object for the directory
    """
    try:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(parents=True)
            logger.debug(f"Created directory: {dir_path}")
        return dir_path
    except Exception as e:
        logger.error(f"Error ensuring directory exists: {e}")
        return Path(directory) 